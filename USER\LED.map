Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.Led) refers to main.o(.data) for .data
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$********) for _printf_f
    main.o(i.main) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.main) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.main) refers to _printf_str.o(.text) for _printf_str
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to usart.o(i.uart1_init) for uart1_init
    main.o(i.main) refers to usart.o(i.uart2_init) for uart2_init
    main.o(i.main) refers to adc.o(i.Adc_Init) for Adc_Init
    main.o(i.main) refers to tim.o(i.tim2_init) for tim2_init
    main.o(i.main) refers to tim.o(i.tim4_init) for tim4_init
    main.o(i.main) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to __2printf.o(.text) for __2printf
    main.o(i.main) refers to main.o(i.task_handler) for task_handler
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.task_handler) refers to main.o(.data) for .data
    main.o(i.task_schedule_callback) refers to main.o(.data) for .data
    main.o(i.tim2_IRQ) refers to adc.o(i.Get_Adc) for Get_Adc
    main.o(i.tim2_IRQ) refers to main.o(.data) for .data
    main.o(i.tim4_IRQ) refers to main.o(i.task_schedule_callback) for task_schedule_callback
    main.o(i.usart1_Rx_IRQ) refers to main.o(.data) for .data
    main.o(i.usart1_send) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.usart1_send) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$********) for _printf_f
    main.o(i.usart1_send) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.usart1_send) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.usart1_send) refers to _printf_str.o(.text) for _printf_str
    main.o(i.usart1_send) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.usart1_send) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.usart1_send) refers to __2printf.o(.text) for __2printf
    main.o(i.usart1_send) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    main.o(i.usart1_send) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.usart1_send) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.usart1_send) refers to main.o(.data) for .data
    main.o(i.usart1_send) refers to main.o(.bss) for .bss
    main.o(i.usart2_Rx_IRQ) refers to main.o(i.Dis_get) for Dis_get
    main.o(i.usart2_Rx_IRQ) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    main.o(i.usart2_Rx_IRQ) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    main.o(i.usart2_Rx_IRQ) refers to main.o(.data) for .data
    main.o(.data) refers to main.o(i.Led) for Led
    main.o(.data) refers to main.o(i.usart1_send) for usart1_send
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for .data
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.IIC_Start1) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Start1) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Stop1) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Stop1) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Wait_Ack1) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Wait_Ack1) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_SHOWCH) refers to oled.o(i.OLED_Set_Pixel) for OLED_Set_Pixel
    oled.o(i.OLED_SHOWCH) refers to oled.o(i.OLED_Display) for OLED_Display
    oled.o(i.OLED_SHOWCH) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_SHOWCHSTR) refers to oled.o(i.OLED_SHOWCH) for OLED_SHOWCH
    oled.o(i.OLED_Set_Pixel) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Write_IIC_Data) for Write_IIC_Data
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Write_IIC_Command) for Write_IIC_Command
    oled.o(i.Write_IIC_Byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.Write_IIC_Byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Start1) for IIC_Start1
    oled.o(i.Write_IIC_Command) refers to oled.o(i.Write_IIC_Byte) for Write_IIC_Byte
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Wait_Ack1) for IIC_Wait_Ack1
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Stop1) for IIC_Stop1
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Start1) for IIC_Start1
    oled.o(i.Write_IIC_Data) refers to oled.o(i.Write_IIC_Byte) for Write_IIC_Byte
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Wait_Ack1) for IIC_Wait_Ack1
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Stop1) for IIC_Stop1
    oled.o(i.fill_picture) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(.data) for .data
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to main.o(i.usart1_Rx_IRQ) for usart1_Rx_IRQ
    usart.o(i.USART2_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART2_IRQHandler) refers to main.o(i.usart2_Rx_IRQ) for usart2_Rx_IRQ
    usart.o(i.USART3_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART3_IRQHandler) refers to usart.o(i.usart3_Rx_IRQ) for usart3_Rx_IRQ
    usart.o(i.USART_printf) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart.o(i.USART_printf) refers to usart.o(i.USART_sendBuf) for USART_sendBuf
    usart.o(i.USART_sendBuf) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._ttywrch) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart1_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart1_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart1_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart2_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart2_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart2_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart2_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart2_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart3_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart3_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart3_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.uart3_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart3_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart3_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart3_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart3_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.usart1_Rx_IRQ) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart2_Rx_IRQ) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart3_Rx_IRQ) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    adc.o(i.Adc_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc_Init) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    adc.o(i.Adc_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_DeInit) for ADC_DeInit
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    adc.o(i.Get_Adc_Average) refers to adc.o(i.Get_Adc) for Get_Adc
    adc.o(i.Get_Adc_Average) refers to delay.o(i.delay_ms) for delay_ms
    tim.o(i.TIM1_UP_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM1_UP_IRQHandler) refers to tim.o(i.tim1_IRQ) for tim1_IRQ
    tim.o(i.TIM1_UP_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM2_IRQHandler) refers to main.o(i.tim2_IRQ) for tim2_IRQ
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM3_IRQHandler) refers to tim.o(i.tim3_IRQ) for tim3_IRQ
    tim.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM4_IRQHandler) refers to main.o(i.tim4_IRQ) for tim4_IRQ
    tim.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.tim1_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tim.o(i.tim1_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim1_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim1_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim.o(i.tim2_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.tim2_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim2_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim2_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim.o(i.tim3_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.tim3_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim3_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim3_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim3_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim.o(i.tim4_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.tim4_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim4_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim4_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim4_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for .data
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$********) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$********) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$********) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$********) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$********) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_f.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$********) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$********) for __rt_entry_sh
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$********) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$********) for .ARM.Collect$$rtentry$$********
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$********) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$********) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$********) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$********) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$********) for .ARM.Collect$$rtexit$$********
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$********) for .ARM.Collect$$rtexit$$********
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$********) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to usart.o(i._ttywrch) for _ttywrch
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.bss), (256 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (100 bytes).
    Removing oled.o(i.Delay_1ms), (18 bytes).
    Removing oled.o(i.Delay_50ms), (20 bytes).
    Removing oled.o(i.IIC_Start1), (52 bytes).
    Removing oled.o(i.IIC_Stop1), (40 bytes).
    Removing oled.o(i.IIC_Wait_Ack1), (32 bytes).
    Removing oled.o(i.OLED_Clear), (58 bytes).
    Removing oled.o(i.OLED_Display), (72 bytes).
    Removing oled.o(i.OLED_Display_Off), (30 bytes).
    Removing oled.o(i.OLED_Display_On), (30 bytes).
    Removing oled.o(i.OLED_DrawBMP), (64 bytes).
    Removing oled.o(i.OLED_Init), (280 bytes).
    Removing oled.o(i.OLED_On), (58 bytes).
    Removing oled.o(i.OLED_SHOWCH), (152 bytes).
    Removing oled.o(i.OLED_SHOWCHSTR), (60 bytes).
    Removing oled.o(i.OLED_Set_Pixel), (36 bytes).
    Removing oled.o(i.OLED_Set_Pos), (42 bytes).
    Removing oled.o(i.OLED_ShowChar), (136 bytes).
    Removing oled.o(i.OLED_ShowNum), (106 bytes).
    Removing oled.o(i.OLED_ShowString), (54 bytes).
    Removing oled.o(i.OLED_WR_Byte), (12 bytes).
    Removing oled.o(i.Write_IIC_Byte), (88 bytes).
    Removing oled.o(i.Write_IIC_Command), (46 bytes).
    Removing oled.o(i.Write_IIC_Data), (46 bytes).
    Removing oled.o(i.fill_picture), (60 bytes).
    Removing oled.o(i.oled_pow), (16 bytes).
    Removing oled.o(.bss), (1024 bytes).
    Removing oled.o(.constdata), (4214 bytes).
    Removing oled.o(.data), (7080 bytes).
    Removing delay.o(i.delay_ms), (52 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing usart.o(i.USART_printf), (42 bytes).
    Removing usart.o(i.USART_sendBuf), (22 bytes).
    Removing usart.o(i._ttywrch), (2 bytes).
    Removing usart.o(i.uart3_init), (168 bytes).
    Removing usart.o(i.usart1_Rx_IRQ), (2 bytes).
    Removing usart.o(i.usart2_Rx_IRQ), (2 bytes).
    Removing adc.o(i.Get_Adc_Average), (46 bytes).
    Removing tim.o(i.tim1_init), (92 bytes).
    Removing tim.o(i.tim2_IRQ), (2 bytes).
    Removing tim.o(i.tim3_init), (92 bytes).
    Removing tim.o(i.tim4_IRQ), (2 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (180 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (28 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (88 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (64 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (116 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (108 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (368 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (300 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (70 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (28 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (32 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (112 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (124 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (60 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (80 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (96 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (36 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (36 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (228 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (58 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).

283 unused section(s) (total 21842 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_md.s           0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\OLED_V1.0.0\oled.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dma.c   0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_exti.c  0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_tim.c   0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\ADC\adc.c                      0x00000000   Number         0  adc.o ABSOLUTE
    ..\SYSTEM\TIM_V1.0.2\tim.c               0x00000000   Number         0  tim.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$********  0x08000160   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$********)
    .ARM.Collect$$_printf_percent$$00000014  0x08000166   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800016c   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000170   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$********          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$********)
    .ARM.Collect$$libinit$$0000000A          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000172   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$********          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$********)
    .ARM.Collect$$libinit$$00000015          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000178   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000182   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000182   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000184   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000186   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$********      0x08000186   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$********)
    .ARM.Collect$$libshutdown$$00000007      0x08000186   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000186   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000186   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000186   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000186   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000188   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000188   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$********          0x08000188   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$********)
    .ARM.Collect$$rtentry$$********          0x0800018e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$********)
    .ARM.Collect$$rtentry$$0000000A          0x0800018e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000192   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000192   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800019a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800019c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$********           0x0800019c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$********)
    .ARM.Collect$$rtexit$$********           0x080001a0   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$********)
    .text                                    0x080001a8   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001e8   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080001ec   Section        0  __2printf.o(.text)
    .text                                    0x08000204   Section        0  __2sprintf.o(.text)
    .text                                    0x08000230   Section        0  _printf_str.o(.text)
    .text                                    0x08000284   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x0800040c   Section        0  heapauxi.o(.text)
    .text                                    0x08000412   Section        2  use_no_semi.o(.text)
    .text                                    0x08000414   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000417   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000834   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000835   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000864   Section        0  _sputc.o(.text)
    .text                                    0x0800086e   Section        0  _printf_char.o(.text)
    .text                                    0x0800089c   Section        0  _printf_char_file.o(.text)
    .text                                    0x080008c0   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080008c8   Section      138  lludiv10.o(.text)
    .text                                    0x08000954   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080009d4   Section        0  bigflt0.o(.text)
    .text                                    0x08000ab8   Section        0  ferror.o(.text)
    .text                                    0x08000ac0   Section        8  libspace.o(.text)
    .text                                    0x08000ac8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000b12   Section        0  exit.o(.text)
    .text                                    0x08000b24   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08000ba4   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08000be2   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08000c28   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08000c88   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08000fc0   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800109c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x080010c6   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080010f0   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_Cmd                                0x08001334   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_DeInit                             0x08001348   Section        0  stm32f10x_adc.o(i.ADC_DeInit)
    i.ADC_GetCalibrationStatus               0x0800138c   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetConversionValue                 0x0800139a   Section        0  stm32f10x_adc.o(i.ADC_GetConversionValue)
    i.ADC_GetFlagStatus                      0x080013a0   Section        0  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    i.ADC_GetResetCalibrationStatus          0x080013ae   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_Init                               0x080013bc   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x08001404   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x08001478   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x08001482   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x08001496   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.Adc_Init                               0x080014a0   Section        0  adc.o(i.Adc_Init)
    i.BusFault_Handler                       0x08001554   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08001556   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Dis_get                                0x08001558   Section        0  main.o(i.Dis_get)
    i.GPIO_Init                              0x08001580   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_SetBits                           0x08001622   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.Get_Adc                                0x08001628   Section        0  adc.o(i.Get_Adc)
    i.HardFault_Handler                      0x08001664   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.LED_Init                               0x08001668   Section        0  led.o(i.LED_Init)
    i.Led                                    0x080016b0   Section        0  main.o(i.Led)
    i.MemManage_Handler                      0x080016c0   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080016c2   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080016c4   Section        0  misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x08001728   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_ADCCLKConfig                       0x0800172c   Section        0  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    i.RCC_APB1PeriphClockCmd                 0x08001740   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001758   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x08001770   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x08001788   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08001818   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClockTo72                        0x0800181c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x0800181d   Thumb Code   160  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x080018c4   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x080018dc   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080018e0   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM1_UP_IRQHandler                     0x08001930   Section        0  tim.o(i.TIM1_UP_IRQHandler)
    i.TIM2_IRQHandler                        0x08001954   Section        0  tim.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08001974   Section        0  tim.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x08001998   Section        0  tim.o(i.TIM4_IRQHandler)
    i.TIM_ClearITPendingBit                  0x080019bc   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x080019c2   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x080019d6   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x080019ee   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_TimeBaseInit                       0x08001a00   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x08001a9c   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08001ac0   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08001ae4   Section        0  usart.o(i.USART3_IRQHandler)
    i.USART_Cmd                              0x08001b08   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08001b1c   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001b5a   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001b8c   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001c38   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08001c40   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08001c42   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08001c6a   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x08001c78   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08001c7c   Section        0  delay.o(i.delay_init)
    i.fputc                                  0x08001cb8   Section        0  usart.o(i.fputc)
    i.main                                   0x08001cd0   Section        0  main.o(i.main)
    i.task_handler                           0x08001d7c   Section        0  main.o(i.task_handler)
    i.task_schedule_callback                 0x08001da8   Section        0  main.o(i.task_schedule_callback)
    i.tim1_IRQ                               0x08001ddc   Section        0  tim.o(i.tim1_IRQ)
    i.tim2_IRQ                               0x08001de0   Section        0  main.o(i.tim2_IRQ)
    i.tim2_init                              0x08001e28   Section        0  tim.o(i.tim2_init)
    i.tim3_IRQ                               0x08001e82   Section        0  tim.o(i.tim3_IRQ)
    i.tim4_IRQ                               0x08001e84   Section        0  main.o(i.tim4_IRQ)
    i.tim4_init                              0x08001e88   Section        0  tim.o(i.tim4_init)
    i.uart1_init                             0x08001ee4   Section        0  usart.o(i.uart1_init)
    i.uart2_init                             0x08001f8c   Section        0  usart.o(i.uart2_init)
    i.usart1_Rx_IRQ                          0x08002030   Section        0  main.o(i.usart1_Rx_IRQ)
    i.usart1_send                            0x08002044   Section        0  main.o(i.usart1_send)
    i.usart2_Rx_IRQ                          0x080021b8   Section        0  main.o(i.usart2_Rx_IRQ)
    i.usart3_Rx_IRQ                          0x08002298   Section        0  usart.o(i.usart3_Rx_IRQ)
    locale$$code                             0x0800229c   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$ddiv                               0x080022c8   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080022cf   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dmul                               0x08002578   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080026cc   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08002768   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08002774   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fdiv                               0x080027cc   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080027cd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$fflt                               0x08002950   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fnaninf                            0x08002980   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08002a0c   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x08002a16   Section        4  printf1.o(x$fpl$printf1)
    .constdata                               0x08002a1a   Section       17  __printf_flags_ss_wp.o(.constdata)
    x$fpl$usenofp                            0x08002a1a   Section        0  usenofp.o(x$fpl$usenofp)
    maptable                                 0x08002a1a   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08002a2c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08002a2c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08002a68   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08002ae0   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08002ae4   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08002aec   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08002af8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08002afa   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08002afb   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08002afc   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       92  main.o(.data)
    g_tcb_tab                                0x20000044   Data          24  main.o(.data)
    .data                                    0x2000005c   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000070   Section        4  delay.o(.data)
    fac_us                                   0x20000070   Data           1  delay.o(.data)
    fac_ms                                   0x20000072   Data           2  delay.o(.data)
    .data                                    0x20000074   Section        4  usart.o(.data)
    .data                                    0x20000078   Section       20  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000078   Data           4  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000007c   Data          16  stm32f10x_rcc.o(.data)
    .bss                                     0x2000008c   Section      256  main.o(.bss)
    .bss                                     0x2000018c   Section       96  libspace.o(.bss)
    HEAP                                     0x200001f0   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x200001f0   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x200003f0   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x200003f0   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x200007f0   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x08000161   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$********)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_s                                0x08000167   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800016d   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000171   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$********)
    __rt_lib_init_rand_1                     0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$********)
    __rt_lib_init_lc_monetary_1              0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000183   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000185   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000187   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000187   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000187   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000187   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000187   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000187   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$********)
    __rt_lib_shutdown_user_alloc_1           0x08000187   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000189   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000189   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000189   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$********)
    __rt_entry_li                            0x0800018f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800018f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$********)
    __rt_entry_main                          0x08000193   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000193   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800019b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800019d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$********)
    __rt_exit_prels_1                        0x0800019d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001a1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$********)
    Reset_Handler                            0x080001a9   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001c5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __use_no_semihosting                     0x080001e9   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x080001ed   Thumb Code    20  __2printf.o(.text)
    __2sprintf                               0x08000205   Thumb Code    38  __2sprintf.o(.text)
    _printf_str                              0x08000231   Thumb Code    82  _printf_str.o(.text)
    __printf                                 0x08000285   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __use_two_region_memory                  0x0800040d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800040f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000411   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000413   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000413   Thumb Code     2  use_no_semi.o(.text)
    __lib_sel_fp_printf                      0x08000415   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080005c7   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x0800083f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000865   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x0800086f   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000883   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000893   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x0800089d   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x080008c1   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x080008c9   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000955   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080009d5   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08000ab9   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08000ac1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000ac1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000ac1   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000ac9   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000b13   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000b25   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08000ba5   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08000be3   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08000c29   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08000c89   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08000fc1   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800109d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x080010c7   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080010f1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_Cmd                                  0x08001335   Thumb Code    20  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_DeInit                               0x08001349   Thumb Code    56  stm32f10x_adc.o(i.ADC_DeInit)
    ADC_GetCalibrationStatus                 0x0800138d   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetConversionValue                   0x0800139b   Thumb Code     6  stm32f10x_adc.o(i.ADC_GetConversionValue)
    ADC_GetFlagStatus                        0x080013a1   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    ADC_GetResetCalibrationStatus            0x080013af   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_Init                                 0x080013bd   Thumb Code    62  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x08001405   Thumb Code   116  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x08001479   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x08001483   Thumb Code    20  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x08001497   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    Adc_Init                                 0x080014a1   Thumb Code   170  adc.o(i.Adc_Init)
    BusFault_Handler                         0x08001555   Thumb Code     2  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08001557   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Dis_get                                  0x08001559   Thumb Code    40  main.o(i.Dis_get)
    GPIO_Init                                0x08001581   Thumb Code   162  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_SetBits                             0x08001623   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    Get_Adc                                  0x08001629   Thumb Code    56  adc.o(i.Get_Adc)
    HardFault_Handler                        0x08001665   Thumb Code     2  stm32f10x_it.o(i.HardFault_Handler)
    LED_Init                                 0x08001669   Thumb Code    66  led.o(i.LED_Init)
    Led                                      0x080016b1   Thumb Code    10  main.o(i.Led)
    MemManage_Handler                        0x080016c1   Thumb Code     2  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080016c3   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x080016c5   Thumb Code    94  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08001729   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_ADCCLKConfig                         0x0800172d   Thumb Code    14  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    RCC_APB1PeriphClockCmd                   0x08001741   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001759   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x08001771   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x08001789   Thumb Code   128  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08001819   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x080018c5   Thumb Code    24  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x080018dd   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080018e1   Thumb Code    64  system_stm32f10x.o(i.SystemInit)
    TIM1_UP_IRQHandler                       0x08001931   Thumb Code    32  tim.o(i.TIM1_UP_IRQHandler)
    TIM2_IRQHandler                          0x08001955   Thumb Code    32  tim.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08001975   Thumb Code    32  tim.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x08001999   Thumb Code    32  tim.o(i.TIM4_IRQHandler)
    TIM_ClearITPendingBit                    0x080019bd   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x080019c3   Thumb Code    20  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x080019d7   Thumb Code    24  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x080019ef   Thumb Code    16  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_TimeBaseInit                         0x08001a01   Thumb Code   114  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x08001a9d   Thumb Code    32  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08001ac1   Thumb Code    32  usart.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08001ae5   Thumb Code    32  usart.o(i.USART3_IRQHandler)
    USART_Cmd                                0x08001b09   Thumb Code    20  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08001b1d   Thumb Code    62  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001b5b   Thumb Code    48  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001b8d   Thumb Code   166  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001c39   Thumb Code     8  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08001c41   Thumb Code     2  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08001c43   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08001c6b   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x08001c79   Thumb Code     2  usart.o(i._sys_exit)
    delay_init                               0x08001c7d   Thumb Code    46  delay.o(i.delay_init)
    fputc                                    0x08001cb9   Thumb Code    18  usart.o(i.fputc)
    main                                     0x08001cd1   Thumb Code   102  main.o(i.main)
    task_handler                             0x08001d7d   Thumb Code    40  main.o(i.task_handler)
    task_schedule_callback                   0x08001da9   Thumb Code    48  main.o(i.task_schedule_callback)
    tim1_IRQ                                 0x08001ddd   Thumb Code     2  tim.o(i.tim1_IRQ)
    tim2_IRQ                                 0x08001de1   Thumb Code    66  main.o(i.tim2_IRQ)
    tim2_init                                0x08001e29   Thumb Code    90  tim.o(i.tim2_init)
    tim3_IRQ                                 0x08001e83   Thumb Code     2  tim.o(i.tim3_IRQ)
    tim4_IRQ                                 0x08001e85   Thumb Code     4  main.o(i.tim4_IRQ)
    tim4_init                                0x08001e89   Thumb Code    88  tim.o(i.tim4_init)
    uart1_init                               0x08001ee5   Thumb Code   158  usart.o(i.uart1_init)
    uart2_init                               0x08001f8d   Thumb Code   154  usart.o(i.uart2_init)
    usart1_Rx_IRQ                            0x08002031   Thumb Code    14  main.o(i.usart1_Rx_IRQ)
    usart1_send                              0x08002045   Thumb Code   202  main.o(i.usart1_send)
    usart2_Rx_IRQ                            0x080021b9   Thumb Code   214  main.o(i.usart2_Rx_IRQ)
    usart3_Rx_IRQ                            0x08002299   Thumb Code     2  usart.o(i.usart3_Rx_IRQ)
    _get_lc_numeric                          0x0800229d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_ddiv                             0x080022c9   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080022c9   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_dmul                             0x08002579   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08002579   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080026cd   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08002769   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08002775   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08002775   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fdiv                             0x080027cd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080027cd   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_i2f                              0x08002951   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08002951   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __fpl_fnaninf                            0x08002981   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08002a0d   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x08002a17   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x08002a1a   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08002ac0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002ae0   Number         0  anon$$obj.o(Region$$Table)
    rx_flag                                  0x20000000   Data           1  main.o(.data)
    stay_flag                                0x20000001   Data           1  main.o(.data)
    myShape                                  0x20000002   Data           1  main.o(.data)
    buff                                     0x20000003   Data           1  main.o(.data)
    Rx_index                                 0x20000004   Data           4  main.o(.data)
    ADC_SUM                                  0x20000008   Data           4  main.o(.data)
    ADC_index                                0x2000000c   Data           4  main.o(.data)
    ADC_COJ                                  0x20000010   Data           4  main.o(.data)
    adc_real                                 0x20000014   Data           4  main.o(.data)
    adcx1                                    0x20000018   Data           4  main.o(.data)
    adcx2                                    0x2000001c   Data           4  main.o(.data)
    dis_data                                 0x20000020   Data           4  main.o(.data)
    Q_data                                   0x20000024   Data           4  main.o(.data)
    i                                        0x20000028   Data           4  main.o(.data)
    Dis_buff                                 0x2000002c   Data           6  main.o(.data)
    R_buff                                   0x20000032   Data           6  main.o(.data)
    T_buff                                   0x20000038   Data           6  main.o(.data)
    Q_buff                                   0x2000003e   Data           6  main.o(.data)
    SystemCoreClock                          0x2000005c   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000060   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x20000074   Data           4  usart.o(.data)
    send_buff                                0x2000008c   Data         256  main.o(.bss)
    __libspace_start                         0x2000018c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001ec   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002b88, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002afc, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO          761    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         2392  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         2716    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         2718    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         2720    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         2389    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x********   Code   RO         2388    .ARM.Collect$$_printf_percent$$********  c_w.l(_printf_f.o)
    0x08000166   0x08000166   0x********   Code   RO         2387    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800016c   0x0800016c   0x********   Code   RO         2480    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000170   0x08000170   0x00000002   Code   RO         2592    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000172   0x08000172   0x00000000   Code   RO         2594    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         2596    .ARM.Collect$$libinit$$********  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         2599    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         2601    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         2603    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x********   Code   RO         2604    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         2606    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         2608    .ARM.Collect$$libinit$$********  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         2610    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x0000000a   Code   RO         2611    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2612    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2614    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2616    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2618    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2620    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2622    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2624    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2626    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2630    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2632    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2634    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000000   Code   RO         2636    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000182   0x08000182   0x00000002   Code   RO         2637    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000002   Code   RO         2668    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000186   0x08000186   0x00000000   Code   RO         2677    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000186   0x08000186   0x00000000   Code   RO         2679    .ARM.Collect$$libshutdown$$********  c_w.l(libshutdown2.o)
    0x08000186   0x08000186   0x00000000   Code   RO         2682    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000186   0x08000186   0x00000000   Code   RO         2685    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000186   0x08000186   0x00000000   Code   RO         2687    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000186   0x08000186   0x00000000   Code   RO         2690    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000186   0x08000186   0x00000002   Code   RO         2691    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000188   0x08000188   0x00000000   Code   RO         2416    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000188   0x08000188   0x00000000   Code   RO         2506    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000188   0x08000188   0x********   Code   RO         2518    .ARM.Collect$$rtentry$$********  c_w.l(__rtentry4.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         2508    .ARM.Collect$$rtentry$$********  c_w.l(__rtentry2.o)
    0x0800018e   0x0800018e   0x********   Code   RO         2509    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000192   0x08000192   0x00000000   Code   RO         2511    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000192   0x08000192   0x00000008   Code   RO         2512    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800019a   0x0800019a   0x00000002   Code   RO         2638    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         2648    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800019c   0x0800019c   0x********   Code   RO         2649    .ARM.Collect$$rtexit$$********  c_w.l(rtexit2.o)
    0x080001a0   0x080001a0   0x********   Code   RO         2650    .ARM.Collect$$rtexit$$********  c_w.l(rtexit2.o)
    0x080001a6   0x080001a6   0x00000002   PAD
    0x080001a8   0x080001a8   0x00000040   Code   RO          762    .text               startup_stm32f10x_md.o
    0x080001e8   0x080001e8   0x00000002   Code   RO         2351    .text               c_w.l(use_no_semi_2.o)
    0x080001ea   0x080001ea   0x00000002   PAD
    0x080001ec   0x080001ec   0x00000018   Code   RO         2355    .text               c_w.l(__2printf.o)
    0x08000204   0x08000204   0x0000002c   Code   RO         2357    .text               c_w.l(__2sprintf.o)
    0x08000230   0x08000230   0x00000052   Code   RO         2365    .text               c_w.l(_printf_str.o)
    0x08000282   0x08000282   0x00000002   PAD
    0x08000284   0x08000284   0x00000188   Code   RO         2384    .text               c_w.l(__printf_flags_ss_wp.o)
    0x0800040c   0x0800040c   0x********   Code   RO         2390    .text               c_w.l(heapauxi.o)
    0x08000412   0x08000412   0x00000002   Code   RO         2414    .text               c_w.l(use_no_semi.o)
    0x08000414   0x08000414   0x0000041e   Code   RO         2425    .text               c_w.l(_printf_fp_dec.o)
    0x08000832   0x08000832   0x00000002   PAD
    0x08000834   0x08000834   0x00000030   Code   RO         2427    .text               c_w.l(_printf_char_common.o)
    0x08000864   0x08000864   0x0000000a   Code   RO         2429    .text               c_w.l(_sputc.o)
    0x0800086e   0x0800086e   0x0000002c   Code   RO         2433    .text               c_w.l(_printf_char.o)
    0x0800089a   0x0800089a   0x00000002   PAD
    0x0800089c   0x0800089c   0x00000024   Code   RO         2435    .text               c_w.l(_printf_char_file.o)
    0x080008c0   0x080008c0   0x00000008   Code   RO         2523    .text               c_w.l(rt_locale_intlibspace.o)
    0x080008c8   0x080008c8   0x0000008a   Code   RO         2525    .text               c_w.l(lludiv10.o)
    0x08000952   0x08000952   0x00000002   PAD
    0x08000954   0x08000954   0x00000080   Code   RO         2532    .text               c_w.l(_printf_fp_infnan.o)
    0x080009d4   0x080009d4   0x000000e4   Code   RO         2538    .text               c_w.l(bigflt0.o)
    0x08000ab8   0x08000ab8   0x00000008   Code   RO         2563    .text               c_w.l(ferror.o)
    0x08000ac0   0x08000ac0   0x00000008   Code   RO         2576    .text               c_w.l(libspace.o)
    0x08000ac8   0x08000ac8   0x0000004a   Code   RO         2579    .text               c_w.l(sys_stackheap_outer.o)
    0x08000b12   0x08000b12   0x00000012   Code   RO         2583    .text               c_w.l(exit.o)
    0x08000b24   0x08000b24   0x00000080   Code   RO         2585    .text               c_w.l(strcmpv7m.o)
    0x08000ba4   0x08000ba4   0x0000003e   Code   RO         2541    CL$$btod_d2e        c_w.l(btod.o)
    0x08000be2   0x08000be2   0x00000046   Code   RO         2543    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08000c28   0x08000c28   0x00000060   Code   RO         2542    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08000c88   0x08000c88   0x00000338   Code   RO         2551    CL$$btod_div_common  c_w.l(btod.o)
    0x08000fc0   0x08000fc0   0x000000dc   Code   RO         2548    CL$$btod_e2e        c_w.l(btod.o)
    0x0800109c   0x0800109c   0x0000002a   Code   RO         2545    CL$$btod_ediv       c_w.l(btod.o)
    0x080010c6   0x080010c6   0x0000002a   Code   RO         2544    CL$$btod_emul       c_w.l(btod.o)
    0x080010f0   0x080010f0   0x00000244   Code   RO         2550    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001334   0x08001334   0x00000014   Code   RO         1835    i.ADC_Cmd           stm32f10x_adc.o
    0x08001348   0x08001348   0x00000044   Code   RO         1837    i.ADC_DeInit        stm32f10x_adc.o
    0x0800138c   0x0800138c   0x0000000e   Code   RO         1843    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x0800139a   0x0800139a   0x********   Code   RO         1844    i.ADC_GetConversionValue  stm32f10x_adc.o
    0x080013a0   0x080013a0   0x0000000e   Code   RO         1846    i.ADC_GetFlagStatus  stm32f10x_adc.o
    0x080013ae   0x080013ae   0x0000000e   Code   RO         1849    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x080013bc   0x080013bc   0x00000048   Code   RO         1853    i.ADC_Init          stm32f10x_adc.o
    0x08001404   0x08001404   0x00000074   Code   RO         1857    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x08001478   0x08001478   0x0000000a   Code   RO         1858    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x08001482   0x08001482   0x00000014   Code   RO         1860    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x08001496   0x08001496   0x0000000a   Code   RO         1862    i.ADC_StartCalibration  stm32f10x_adc.o
    0x080014a0   0x080014a0   0x000000b4   Code   RO          647    i.Adc_Init          adc.o
    0x08001554   0x08001554   0x00000002   Code   RO          197    i.BusFault_Handler  stm32f10x_it.o
    0x08001556   0x08001556   0x00000002   Code   RO          198    i.DebugMon_Handler  stm32f10x_it.o
    0x08001558   0x08001558   0x00000028   Code   RO            1    i.Dis_get           main.o
    0x08001580   0x08001580   0x000000a2   Code   RO          772    i.GPIO_Init         stm32f10x_gpio.o
    0x08001622   0x08001622   0x********   Code   RO          780    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08001626   0x08001626   0x00000002   PAD
    0x08001628   0x08001628   0x0000003c   Code   RO          648    i.Get_Adc           adc.o
    0x08001664   0x08001664   0x00000002   Code   RO          199    i.HardFault_Handler  stm32f10x_it.o
    0x08001666   0x08001666   0x00000002   PAD
    0x08001668   0x08001668   0x00000048   Code   RO          315    i.LED_Init          led.o
    0x080016b0   0x080016b0   0x00000010   Code   RO            2    i.Led               main.o
    0x080016c0   0x080016c0   0x00000002   Code   RO          200    i.MemManage_Handler  stm32f10x_it.o
    0x080016c2   0x080016c2   0x00000002   Code   RO          201    i.NMI_Handler       stm32f10x_it.o
    0x080016c4   0x080016c4   0x00000064   Code   RO         1260    i.NVIC_Init         misc.o
    0x08001728   0x08001728   0x00000002   Code   RO          202    i.PendSV_Handler    stm32f10x_it.o
    0x0800172a   0x0800172a   0x00000002   PAD
    0x0800172c   0x0800172c   0x00000014   Code   RO          880    i.RCC_ADCCLKConfig  stm32f10x_rcc.o
    0x08001740   0x08001740   0x00000018   Code   RO          882    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001758   0x08001758   0x00000018   Code   RO          884    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001770   0x08001770   0x00000018   Code   RO          885    i.RCC_APB2PeriphResetCmd  stm32f10x_rcc.o
    0x08001788   0x08001788   0x00000090   Code   RO          892    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08001818   0x08001818   0x00000002   Code   RO          203    i.SVC_Handler       stm32f10x_it.o
    0x0800181a   0x0800181a   0x00000002   PAD
    0x0800181c   0x0800181c   0x000000a8   Code   RO          286    i.SetSysClockTo72   system_stm32f10x.o
    0x080018c4   0x080018c4   0x00000018   Code   RO         1264    i.SysTick_CLKSourceConfig  misc.o
    0x080018dc   0x080018dc   0x00000002   Code   RO          204    i.SysTick_Handler   stm32f10x_it.o
    0x080018de   0x080018de   0x00000002   PAD
    0x080018e0   0x080018e0   0x00000050   Code   RO          288    i.SystemInit        system_stm32f10x.o
    0x08001930   0x08001930   0x00000024   Code   RO          671    i.TIM1_UP_IRQHandler  tim.o
    0x08001954   0x08001954   0x00000020   Code   RO          672    i.TIM2_IRQHandler   tim.o
    0x08001974   0x08001974   0x00000024   Code   RO          673    i.TIM3_IRQHandler   tim.o
    0x08001998   0x08001998   0x00000024   Code   RO          674    i.TIM4_IRQHandler   tim.o
    0x080019bc   0x080019bc   0x********   Code   RO         1305    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x080019c2   0x080019c2   0x00000014   Code   RO         1310    i.TIM_Cmd           stm32f10x_tim.o
    0x080019d6   0x080019d6   0x00000018   Code   RO         1331    i.TIM_GetITStatus   stm32f10x_tim.o
    0x080019ee   0x080019ee   0x00000010   Code   RO         1335    i.TIM_ITConfig      stm32f10x_tim.o
    0x080019fe   0x080019fe   0x00000002   PAD
    0x08001a00   0x08001a00   0x0000009c   Code   RO         1381    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001a9c   0x08001a9c   0x00000024   Code   RO          544    i.USART1_IRQHandler  usart.o
    0x08001ac0   0x08001ac0   0x00000024   Code   RO          545    i.USART2_IRQHandler  usart.o
    0x08001ae4   0x08001ae4   0x00000024   Code   RO          546    i.USART3_IRQHandler  usart.o
    0x08001b08   0x08001b08   0x00000014   Code   RO         1084    i.USART_Cmd         stm32f10x_usart.o
    0x08001b1c   0x08001b1c   0x0000003e   Code   RO         1088    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001b5a   0x08001b5a   0x00000030   Code   RO         1090    i.USART_ITConfig    stm32f10x_usart.o
    0x08001b8a   0x08001b8a   0x00000002   PAD
    0x08001b8c   0x08001b8c   0x000000ac   Code   RO         1091    i.USART_Init        stm32f10x_usart.o
    0x08001c38   0x08001c38   0x00000008   Code   RO         1098    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001c40   0x08001c40   0x00000002   Code   RO          205    i.UsageFault_Handler  stm32f10x_it.o
    0x08001c42   0x08001c42   0x00000028   Code   RO         2574    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08001c6a   0x08001c6a   0x0000000e   Code   RO         2377    i._is_digit         c_w.l(__printf_wp.o)
    0x08001c78   0x08001c78   0x00000002   Code   RO          549    i._sys_exit         usart.o
    0x08001c7a   0x08001c7a   0x00000002   PAD
    0x08001c7c   0x08001c7c   0x0000003c   Code   RO          489    i.delay_init        delay.o
    0x08001cb8   0x08001cb8   0x00000018   Code   RO          551    i.fputc             usart.o
    0x08001cd0   0x08001cd0   0x000000ac   Code   RO            3    i.main              main.o
    0x08001d7c   0x08001d7c   0x0000002c   Code   RO            4    i.task_handler      main.o
    0x08001da8   0x08001da8   0x00000034   Code   RO            5    i.task_schedule_callback  main.o
    0x08001ddc   0x08001ddc   0x00000002   Code   RO          675    i.tim1_IRQ          tim.o
    0x08001dde   0x08001dde   0x00000002   PAD
    0x08001de0   0x08001de0   0x00000048   Code   RO            6    i.tim2_IRQ          main.o
    0x08001e28   0x08001e28   0x0000005a   Code   RO          678    i.tim2_init         tim.o
    0x08001e82   0x08001e82   0x00000002   Code   RO          679    i.tim3_IRQ          tim.o
    0x08001e84   0x08001e84   0x********   Code   RO            7    i.tim4_IRQ          main.o
    0x08001e88   0x08001e88   0x0000005c   Code   RO          682    i.tim4_init         tim.o
    0x08001ee4   0x08001ee4   0x000000a8   Code   RO          552    i.uart1_init        usart.o
    0x08001f8c   0x08001f8c   0x000000a4   Code   RO          553    i.uart2_init        usart.o
    0x08002030   0x08002030   0x00000014   Code   RO            8    i.usart1_Rx_IRQ     main.o
    0x08002044   0x08002044   0x00000174   Code   RO            9    i.usart1_send       main.o
    0x080021b8   0x080021b8   0x000000e0   Code   RO           10    i.usart2_Rx_IRQ     main.o
    0x08002298   0x08002298   0x00000002   Code   RO          557    i.usart3_Rx_IRQ     usart.o
    0x0800229a   0x0800229a   0x00000002   PAD
    0x0800229c   0x0800229c   0x0000002c   Code   RO         2570    locale$$code        c_w.l(lc_numeric_c.o)
    0x080022c8   0x080022c8   0x000002b0   Code   RO         2395    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08002578   0x08002578   0x00000154   Code   RO         2398    x$fpl$dmul          fz_ws.l(dmul.o)
    0x080026cc   0x080026cc   0x0000009c   Code   RO         2490    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08002768   0x08002768   0x0000000c   Code   RO         2492    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08002774   0x08002774   0x00000056   Code   RO         2400    x$fpl$f2d           fz_ws.l(f2d.o)
    0x080027ca   0x080027ca   0x00000002   PAD
    0x080027cc   0x080027cc   0x00000184   Code   RO         2403    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08002950   0x08002950   0x00000030   Code   RO         2407    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08002980   0x08002980   0x0000008c   Code   RO         2494    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08002a0c   0x08002a0c   0x0000000a   Code   RO         2496    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08002a16   0x08002a16   0x********   Code   RO         2412    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08002a1a   0x08002a1a   0x00000000   Code   RO         2504    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08002a1a   0x08002a1a   0x00000011   Data   RO         2385    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08002a2b   0x08002a2b   0x00000001   PAD
    0x08002a2c   0x08002a2c   0x00000094   Data   RO         2539    .constdata          c_w.l(bigflt0.o)
    0x08002ac0   0x08002ac0   0x00000020   Data   RO         2714    Region$$Table       anon$$obj.o
    0x08002ae0   0x08002ae0   0x0000001c   Data   RO         2569    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002afc, Size: 0x000007f0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002afc   0x0000005c   Data   RW           13    .data               main.o
    0x2000005c   0x08002b58   0x00000014   Data   RW          289    .data               system_stm32f10x.o
    0x20000070   0x08002b6c   0x********   Data   RW          492    .data               delay.o
    0x20000074   0x08002b70   0x********   Data   RW          558    .data               usart.o
    0x20000078   0x08002b74   0x00000014   Data   RW          912    .data               stm32f10x_rcc.o
    0x2000008c        -       0x00000100   Zero   RW           12    .bss                main.o
    0x2000018c        -       0x00000060   Zero   RW         2577    .bss                c_w.l(libspace.o)
    0x200001ec   0x08002b88   0x********   PAD
    0x200001f0        -       0x00000200   Zero   RW          760    HEAP                startup_stm32f10x_md.o
    0x200003f0        -       0x00000400   Zero   RW          759    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       240         14          0          0          0       1173   adc.o
        60         14          0          4          0        633   delay.o
        72          6          0          0          0        479   led.o
      1016        280          0         92        256     266992   main.o
       124          6          0          0          0       1462   misc.o
        64         26        236          0       1536        780   startup_stm32f10x_md.o
       364         22          0          0          0       8390   stm32f10x_adc.o
       166          0          0          0          0       2335   stm32f10x_gpio.o
        18          0          0          0          0       3486   stm32f10x_it.o
       236         40          0         20          0       5442   stm32f10x_rcc.o
       222         42          0          0          0       4325   stm32f10x_tim.o
       310          6          0          0          0       4898   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       248         24          0         20          0       1633   system_stm32f10x.o
       326         16          0          0          0       3945   tim.o
       468         38          0          4          0       6468   usart.o

    ----------------------------------------------------------------------
      3954        <USER>        <GROUP>        140       1792     312473   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        24          4          0          0          0         84   __2printf.o
        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       688        140          0          0          0        208   ddiv.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       388         76          0          0          0         96   fdiv.o
        48          0          0          0          0         68   fflt_clz.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      6588        <USER>        <GROUP>          0        100       3780   Library Totals
        16          0          1          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4660        198        193          0         96       2772   c_w.l
      1872        240          0          0          0        940   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      6588        <USER>        <GROUP>          0        100       3780   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10542        972        462        140       1892     310737   Grand Totals
     10542        972        462        140       1892     310737   ELF Image Totals
     10542        972        462        140          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                11004 (  10.75kB)
    Total RW  Size (RW Data + ZI Data)              2032 (   1.98kB)
    Total ROM Size (Code + RO Data + RW Data)      11144 (  10.88kB)

==============================================================================

